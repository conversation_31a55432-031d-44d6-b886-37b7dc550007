AWNSER_SCHEMA = {
    "type": "object",
    "properties": {
        "人工服务": {"type": "boolean"},
        "解释": {"type": "string"},
        "回复": {"type": "string"},
        "知识库关联度": {"type": "integer"},
    },
    "required": ["人工服务", "解释", "回复", "知识库关联度"],
}
NEW_AWNSER_SCHEMA = {
    "type": "object",
    "properties": {
        "人工服务": {"type": "boolean"},
        "解释": {"type": "string"},
        "回复内容": {"type": "string"},
        "回复": {"type": "string"},
        "知识库关联度": {"type": "integer"},
    },
    "required": ["人工服务", "解释", "回复内容", "回复", "知识库关联度"],
}
PRE_PROCESS_SCHEMA = {
    "type": "object",
    "properties": {
        "客户的最新消息": {"type": "string"},
        "重构的客户消息": {"type": "string"},
    },
    "required": ["客户的最新消息", "重构的客户消息"],
}
NEW_PRE_PROCESS_SCHEMA = {
    "type": "object",
    "properties": {
        "咨询的商品": {"type": "string"},
        "客户的最新消息": {"type": "string"},
        "重构的客户消息": {"type": "string"},
    },
    "required": ["咨询的商品", "客户的最新消息", "重构的客户消息"],
}