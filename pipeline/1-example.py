from message_prossor.utils import (
load_json,
spilit_log_message
)
from message_prossor.jieba_utils import (
    jieba_optimized_common_substring
)

def main():
    data = load_json.load_jsonl(
        './data/阿里云改写日志.json'
    )
    log_data, error_data = spilit_log_message.split_log_pre_message(data)
    enhanced_human_data, word_frequency = jieba_optimized_common_substring.extract_keywords_from_pre_user_data(log_data)
    print(data)
    

if __name__ == "__main__":
    main()