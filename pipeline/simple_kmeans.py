"""
简化版K-means聚类分析
使用基础库实现，减少依赖
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from message_prossor.utils import (
    load_json,
    spilit_log_message
)
import jieba
import re
import json
from collections import Counter, defaultdict
import random
import math

def preprocess_text(text):
    """
    文本预处理函数
    """
    if not isinstance(text, str):
        return ""
    
    # 去除特殊字符，保留中文、英文、数字
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
    
    # 去除多余空格
    text = re.sub(r'\s+', ' ', text).strip()
    
    # 使用jieba分词
    words = jieba.cut(text)
    
    # 过滤停用词和短词
    stop_words = {
        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', 
        '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', 
        '没有', '看', '好', '自己', '这', '那', '个', '为', '能', '可以',
        '什么', '怎么', '如何', '吗', '呢', '吧', '啊', '呀'
    }
    filtered_words = [word.strip() for word in words 
                     if len(word.strip()) > 1 and word.strip() not in stop_words]
    
    return filtered_words

def build_vocabulary(texts, min_freq=2):
    """
    构建词汇表
    """
    word_freq = Counter()
    for text in texts:
        words = preprocess_text(text)
        word_freq.update(words)
    
    # 过滤低频词
    vocab = {word for word, freq in word_freq.items() if freq >= min_freq}
    vocab_list = sorted(list(vocab))
    word_to_idx = {word: idx for idx, word in enumerate(vocab_list)}
    
    return vocab_list, word_to_idx

def text_to_vector(text, word_to_idx):
    """
    将文本转换为向量（词频向量）
    """
    words = preprocess_text(text)
    vector = [0] * len(word_to_idx)
    
    for word in words:
        if word in word_to_idx:
            vector[word_to_idx[word]] += 1
    
    # 归一化
    total = sum(vector)
    if total > 0:
        vector = [x / total for x in vector]
    
    return vector

def cosine_distance(vec1, vec2):
    """
    计算余弦距离
    """
    dot_product = sum(a * b for a, b in zip(vec1, vec2))
    norm1 = math.sqrt(sum(a * a for a in vec1))
    norm2 = math.sqrt(sum(a * a for a in vec2))
    
    if norm1 == 0 or norm2 == 0:
        return 1.0
    
    cosine_sim = dot_product / (norm1 * norm2)
    return 1 - cosine_sim

def simple_kmeans(vectors, k, max_iters=100, random_state=42):
    """
    简单的K-means实现
    """
    random.seed(random_state)
    n_samples = len(vectors)
    n_features = len(vectors[0])
    
    # 随机初始化聚类中心
    centers = []
    for _ in range(k):
        center = [random.random() for _ in range(n_features)]
        centers.append(center)
    
    for iteration in range(max_iters):
        # 分配样本到最近的聚类中心
        clusters = [[] for _ in range(k)]
        labels = []
        
        for i, vector in enumerate(vectors):
            distances = [cosine_distance(vector, center) for center in centers]
            closest_center = distances.index(min(distances))
            clusters[closest_center].append(i)
            labels.append(closest_center)
        
        # 更新聚类中心
        new_centers = []
        for cluster_indices in clusters:
            if cluster_indices:
                # 计算平均向量
                center = [0] * n_features
                for idx in cluster_indices:
                    for j in range(n_features):
                        center[j] += vectors[idx][j]
                center = [x / len(cluster_indices) for x in center]
                new_centers.append(center)
            else:
                # 如果聚类为空，保持原中心
                new_centers.append(centers[len(new_centers)])
        
        # 检查收敛
        converged = True
        for i in range(k):
            if cosine_distance(centers[i], new_centers[i]) > 1e-6:
                converged = False
                break
        
        centers = new_centers
        
        if converged:
            print(f"K-means在第{iteration+1}次迭代后收敛")
            break
    
    return labels, centers

def analyze_clusters_simple(texts, labels, vocab_list, word_to_idx, k):
    """
    分析聚类结果
    """
    cluster_info = {}
    
    for cluster_id in range(k):
        cluster_texts = [texts[i] for i, label in enumerate(labels) if label == cluster_id]
        
        if not cluster_texts:
            cluster_info[cluster_id] = {
                'size': 0,
                'percentage': 0,
                'top_words': [],
                'sample_texts': []
            }
            continue
        
        # 统计该聚类中的词频
        word_freq = Counter()
        for text in cluster_texts:
            words = preprocess_text(text)
            word_freq.update(words)
        
        # 获取高频词
        top_words = word_freq.most_common(10)
        
        cluster_info[cluster_id] = {
            'size': len(cluster_texts),
            'percentage': len(cluster_texts) / len(texts) * 100,
            'top_words': top_words,
            'sample_texts': cluster_texts[:5]  # 前5个样本
        }
    
    return cluster_info

def perform_simple_kmeans_clustering(log_data, n_clusters=5):
    """
    执行简单的K-means聚类
    """
    # 提取用户请求文本
    user_requests = []
    valid_indices = []
    
    print("正在提取用户请求文本...")
    for i, item in enumerate(log_data):
        try:
            user_request = item['message']['user_request']
            if user_request and isinstance(user_request, str) and len(user_request.strip()) > 0:
                user_requests.append(user_request.strip())
                valid_indices.append(i)
        except (KeyError, TypeError) as e:
            print(f"索引 {i} 处理失败: {e}")
            continue
    
    print(f"成功提取 {len(user_requests)} 条有效用户请求")
    
    if len(user_requests) < n_clusters:
        print(f"警告: 有效数据量({len(user_requests)})少于聚类数量({n_clusters})")
        n_clusters = max(2, len(user_requests) // 2)
        print(f"调整聚类数量为: {n_clusters}")
    
    # 构建词汇表
    print("正在构建词汇表...")
    vocab_list, word_to_idx = build_vocabulary(user_requests, min_freq=2)
    print(f"词汇表大小: {len(vocab_list)}")
    
    # 将文本转换为向量
    print("正在将文本转换为向量...")
    vectors = [text_to_vector(text, word_to_idx) for text in user_requests]
    
    # 执行K-means聚类
    print(f"正在执行K-means聚类 (k={n_clusters})...")
    labels, centers = simple_kmeans(vectors, n_clusters)
    
    # 分析聚类结果
    print("正在分析聚类结果...")
    cluster_info = analyze_clusters_simple(user_requests, labels, vocab_list, word_to_idx, n_clusters)
    
    return {
        'user_requests': user_requests,
        'labels': labels,
        'cluster_info': cluster_info,
        'n_clusters': n_clusters,
        'vocab_list': vocab_list
    }

def print_simple_cluster_analysis(results):
    """
    打印简单聚类分析结果
    """
    cluster_info = results['cluster_info']
    n_clusters = results['n_clusters']
    
    print("\n" + "="*80)
    print(f"简单K-means聚类分析结果 (k={n_clusters})")
    print("="*80)
    print(f"总数据量: {len(results['user_requests'])}")
    
    for cluster_id in range(n_clusters):
        info = cluster_info[cluster_id]
        print(f"\n【聚类 {cluster_id}】")
        print(f"数据量: {info['size']} ({info['percentage']:.1f}%)")
        
        print("高频词汇:")
        for word, freq in info['top_words'][:5]:
            print(f"  - {word}: {freq}次")
        
        print("样本文本:")
        for i, text in enumerate(info['sample_texts'][:3], 1):
            print(f"  {i}. {text[:100]}{'...' if len(text) > 100 else ''}")
        print()

def save_simple_results(results, filename_prefix="simple_clustering"):
    """
    保存简单聚类结果
    """
    # 保存为JSON格式
    output_data = {
        'n_clusters': results['n_clusters'],
        'total_samples': len(results['user_requests']),
        'clusters': []
    }
    
    for cluster_id in range(results['n_clusters']):
        info = results['cluster_info'][cluster_id]
        cluster_data = {
            'cluster_id': cluster_id,
            'size': info['size'],
            'percentage': info['percentage'],
            'top_words': info['top_words'],
            'sample_texts': info['sample_texts']
        }
        output_data['clusters'].append(cluster_data)
    
    # 保存聚类摘要
    with open(f"{filename_prefix}_summary.json", 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    # 保存详细结果
    detailed_results = []
    for i, (text, label) in enumerate(zip(results['user_requests'], results['labels'])):
        detailed_results.append({
            'index': i,
            'text': text,
            'cluster': label
        })
    
    with open(f"{filename_prefix}_detailed.json", 'w', encoding='utf-8') as f:
        json.dump(detailed_results, f, ensure_ascii=False, indent=2)
    
    print(f"结果已保存到: {filename_prefix}_summary.json 和 {filename_prefix}_detailed.json")

def main():
    """
    主函数
    """
    print("开始简单K-means聚类分析...")
    
    # 加载数据
    data = load_json.load_jsonl('./data/阿里云改写日志.json')
    log_data, error_data = spilit_log_message.split_log_pre_message(data)
    
    print(f"成功加载数据: {len(data)} 条原始数据")
    print(f"有效日志数据: {len(log_data)} 条")
    print(f"错误数据: {len(error_data)} 条")
    
    if len(log_data) == 0:
        print("没有有效的日志数据，程序退出")
        return
    
    # 尝试不同的k值
    for k in [3, 5, 8]:
        print(f"\n{'='*60}")
        print(f"尝试 k={k} 的聚类")
        print('='*60)
        
        try:
            results = perform_simple_kmeans_clustering(log_data, n_clusters=k)
            print_simple_cluster_analysis(results)
            save_simple_results(results, f"simple_clustering_k{k}")
        except Exception as e:
            print(f"k={k} 聚类失败: {e}")
    
    print("\n简单K-means聚类分析完成！")

if __name__ == "__main__":
    main()
