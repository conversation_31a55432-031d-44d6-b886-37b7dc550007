# K-means聚类分析说明

本目录包含了对用户请求进行K-means聚类分析的代码，以`log_data[0]['message']['user_request']`字段为聚类列。

## 文件说明

### 1. `kmeans_clustering.py`
完整版K-means聚类模块，使用scikit-learn等专业库实现。

**功能特性：**
- 使用TF-IDF向量化文本
- 支持轮廓系数评估
- PCA降维可视化
- 自动寻找最优聚类数量
- 详细的聚类特征分析

**依赖包：**
```bash
pip install numpy pandas scikit-learn matplotlib seaborn jieba
```

### 2. `run_kmeans_analysis.py`
完整版聚类分析的主程序。

**功能：**
- 自动寻找最优k值
- 生成可视化图表
- 保存详细结果到CSV
- 生成聚类摘要报告

### 3. `simple_kmeans.py`
简化版K-means聚类，使用基础Python库实现。

**功能特性：**
- 不依赖scikit-learn
- 使用余弦距离
- 基础的词频向量化
- 简单的聚类分析

**依赖包：**
```bash
pip install jieba
```

## 使用方法

### 方法1：使用完整版（推荐）

1. 安装依赖：
```bash
pip install numpy pandas scikit-learn matplotlib seaborn jieba
```

2. 运行分析：
```bash
cd pipeline
python run_kmeans_analysis.py
```

### 方法2：使用简化版

1. 安装依赖：
```bash
pip install jieba
```

2. 运行分析：
```bash
cd pipeline
python simple_kmeans.py
```

## 输出文件

### 完整版输出：
- `user_request_clusters_k{k}.png` - 聚类可视化图表
- `user_request_clustering_results_k{k}.csv` - 详细聚类结果
- `cluster_summary_k{k}.txt` - 聚类摘要报告

### 简化版输出：
- `simple_clustering_k{k}_summary.json` - 聚类摘要
- `simple_clustering_k{k}_detailed.json` - 详细结果

## 代码示例

### 基本使用
```python
from kmeans_clustering import perform_kmeans_clustering, print_cluster_analysis

# 执行聚类
results = perform_kmeans_clustering(log_data, n_clusters=5)

# 打印分析结果
print_cluster_analysis(results)
```

### 寻找最优k值
```python
from kmeans_clustering import find_optimal_k

# 寻找最优聚类数量
evaluation_results, best_k = find_optimal_k(log_data, k_range=range(2, 11))
print(f"推荐的最优k值: {best_k}")
```

### 可视化聚类结果
```python
from kmeans_clustering import visualize_clusters

# 生成可视化图表
visualize_clusters(results, save_path='clusters.png')
```

## 聚类分析结果解读

### 轮廓系数 (Silhouette Score)
- 范围：-1 到 1
- 越接近1表示聚类效果越好
- 一般认为 > 0.5 为较好的聚类效果

### 聚类特征词
每个聚类会显示最重要的特征词，这些词能够代表该聚类的主题特征。

### 样本文本
展示每个聚类中的典型用户请求样本，帮助理解聚类的实际含义。

## 参数调整

### 主要参数说明：

- `n_clusters`: 聚类数量
- `max_features`: TF-IDF最大特征数（默认1000）
- `random_state`: 随机种子（确保结果可重现）
- `min_df`: 词汇最小文档频率
- `max_df`: 词汇最大文档频率

### 调整建议：

1. **聚类数量过多**：如果数据被分得过细，可以减少k值
2. **聚类数量过少**：如果聚类过于宽泛，可以增加k值
3. **特征词不够明显**：可以调整`min_df`和`max_df`参数
4. **内存不足**：可以减少`max_features`参数

## 故障排除

### 常见问题：

1. **导入错误**：确保安装了所有依赖包
2. **数据量不足**：如果数据量小于聚类数量，程序会自动调整
3. **中文显示问题**：确保系统支持中文字体
4. **内存不足**：可以使用简化版或减少特征数量

### 解决方案：

1. 使用简化版：`python simple_kmeans.py`
2. 减少数据量：在代码中添加数据采样
3. 调整参数：减少`max_features`或`n_clusters`

## 扩展功能

### 自定义停用词
在`preprocess_text`函数中修改`stop_words`集合。

### 添加新的距离度量
在简化版中可以替换`cosine_distance`函数。

### 自定义特征提取
可以修改文本预处理和向量化方法。

## 注意事项

1. 确保数据文件路径正确：`./data/阿里云改写日志.json`
2. 聚类结果具有随机性，可以通过设置`random_state`确保可重现性
3. 大数据量可能需要较长处理时间
4. 建议先用小样本测试，确认无误后再处理全量数据
