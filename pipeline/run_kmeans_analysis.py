"""
运行K-means聚类分析的主程序
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from message_prossor.utils import (
    load_json,
    spilit_log_message
)
from kmeans_clustering import (
    perform_kmeans_clustering,
    visualize_clusters,
    print_cluster_analysis,
    find_optimal_k
)

def main():
    """
    主函数：执行完整的K-means聚类分析流程
    """
    print("开始加载数据...")
    
    # 加载数据
    data = load_json.load_jsonl('./data/阿里云改写日志.json')
    log_data, error_data = spilit_log_message.split_log_pre_message(data)
    
    print(f"成功加载数据: {len(data)} 条原始数据")
    print(f"有效日志数据: {len(log_data)} 条")
    print(f"错误数据: {len(error_data)} 条")
    
    if len(log_data) == 0:
        print("没有有效的日志数据，程序退出")
        return
    
    # 1. 寻找最优聚类数量
    print("\n" + "="*60)
    print("第一步：寻找最优聚类数量")
    print("="*60)
    
    try:
        evaluation_results, best_k = find_optimal_k(
            log_data, 
            k_range=range(2, 11), 
            max_features=1000
        )
    except Exception as e:
        print(f"寻找最优k值时出错: {e}")
        best_k = 5  # 使用默认值
        print(f"使用默认聚类数量: {best_k}")
    
    # 2. 使用最优k值进行详细聚类分析
    print("\n" + "="*60)
    print(f"第二步：使用最优k值({best_k})进行详细聚类分析")
    print("="*60)
    
    clustering_results = perform_kmeans_clustering(
        log_data, 
        n_clusters=best_k, 
        max_features=1000,
        random_state=42
    )
    
    # 3. 打印分析结果
    print_cluster_analysis(clustering_results)
    
    # 4. 可视化结果
    print("\n正在生成可视化图表...")
    try:
        visualize_clusters(
            clustering_results, 
            save_path=f'user_request_clusters_k{best_k}.png'
        )
    except Exception as e:
        print(f"可视化时出错: {e}")
    
    # 5. 保存详细结果
    print("\n正在保存结果...")
    try:
        results_df = clustering_results['results_df']
        results_df.to_csv(
            f'user_request_clustering_results_k{best_k}.csv', 
            index=False, 
            encoding='utf-8-sig'
        )
        print(f"详细聚类结果已保存到: user_request_clustering_results_k{best_k}.csv")
        
        # 保存聚类摘要
        save_cluster_summary(clustering_results, f'cluster_summary_k{best_k}.txt')
        
    except Exception as e:
        print(f"保存结果时出错: {e}")
    
    # 6. 额外分析：尝试其他k值进行对比
    print("\n" + "="*60)
    print("第三步：对比分析不同k值的聚类效果")
    print("="*60)
    
    compare_k_values = [3, 5, 8]
    if best_k not in compare_k_values:
        compare_k_values.append(best_k)
    
    for k in sorted(set(compare_k_values)):
        if k == best_k:
            continue  # 已经分析过了
            
        print(f"\n分析 k={k} 的聚类效果...")
        try:
            results = perform_kmeans_clustering(
                log_data, 
                n_clusters=k, 
                max_features=1000,
                random_state=42
            )
            
            print(f"k={k} 聚类结果摘要:")
            print(f"  轮廓系数: {results['silhouette_score']:.4f}")
            print(f"  数据量: {len(results['results_df'])}")
            
            # 保存结果
            results['results_df'].to_csv(
                f'user_request_clustering_results_k{k}.csv', 
                index=False, 
                encoding='utf-8-sig'
            )
            
        except Exception as e:
            print(f"分析 k={k} 时出错: {e}")
    
    print("\n" + "="*60)
    print("K-means聚类分析完成！")
    print("="*60)
    print("生成的文件:")
    print(f"- user_request_clusters_k{best_k}.png (可视化图表)")
    print(f"- user_request_clustering_results_k{best_k}.csv (详细结果)")
    print(f"- cluster_summary_k{best_k}.txt (聚类摘要)")
    for k in compare_k_values:
        if k != best_k:
            print(f"- user_request_clustering_results_k{k}.csv (k={k}的结果)")

def save_cluster_summary(clustering_results, filename):
    """
    保存聚类摘要到文件
    
    Args:
        clustering_results: 聚类结果字典
        filename: 保存文件名
    """
    cluster_info = clustering_results['cluster_info']
    silhouette_score_val = clustering_results['silhouette_score']
    n_clusters = clustering_results['n_clusters']
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("K-means聚类分析摘要报告\n")
        f.write("="*50 + "\n\n")
        f.write(f"聚类数量: {n_clusters}\n")
        f.write(f"轮廓系数: {silhouette_score_val:.4f}\n")
        f.write(f"总数据量: {len(clustering_results['results_df'])}\n\n")
        
        for cluster_id in range(n_clusters):
            info = cluster_info[cluster_id]
            f.write(f"【聚类 {cluster_id}】\n")
            f.write(f"数据量: {info['size']} ({info['percentage']:.1f}%)\n")
            
            f.write("主要特征词:\n")
            for feature, score in info['top_features'][:10]:
                f.write(f"  - {feature}: {score:.4f}\n")
            
            f.write("样本文本:\n")
            for i, text in enumerate(info['sample_texts'][:5], 1):
                f.write(f"  {i}. {text}\n")
            f.write("\n" + "-"*40 + "\n\n")
    
    print(f"聚类摘要已保存到: {filename}")

def analyze_specific_cluster(clustering_results, cluster_id):
    """
    分析特定聚类的详细信息
    
    Args:
        clustering_results: 聚类结果字典
        cluster_id: 聚类ID
    """
    if cluster_id not in clustering_results['cluster_info']:
        print(f"聚类 {cluster_id} 不存在")
        return
    
    info = clustering_results['cluster_info'][cluster_id]
    results_df = clustering_results['results_df']
    cluster_data = results_df[results_df['cluster'] == cluster_id]
    
    print(f"\n聚类 {cluster_id} 详细分析:")
    print(f"数据量: {info['size']} ({info['percentage']:.1f}%)")
    
    print("\n主要特征词:")
    for feature, score in info['top_features']:
        print(f"  - {feature}: {score:.4f}")
    
    print(f"\n所有文本 ({len(info['all_texts'])} 条):")
    for i, text in enumerate(info['all_texts'], 1):
        print(f"  {i}. {text}")

if __name__ == "__main__":
    main()
