[project]
name = "message-prossor"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
authors = [
    { name = "“wanlige”", email = "<EMAIL>" }
]
requires-python = ">=3.13"
dependencies = [
    "fastjsonschema>=2.21.1",
    "ijson>=3.4.0",
    "jieba>=0.42.1",
    "json-repair>=0.48.0",
    "ruff>=0.12.7",
    "tqdm>=4.67.1",
]

[build-system]
requires = ["uv_build>=0.8.3,<0.9.0"]
build-backend = "uv_build"
