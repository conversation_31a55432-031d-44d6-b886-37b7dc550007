import json_repair
from tqdm import tqdm


def split_log_message(data):
    error_data = []
    checked_data = []
    for i in tqdm(range(len(data)), total=len(data), desc="Spliting log message"):
        try:
            data[i]['context'] = json_repair.loads(data[i]["context"])
            schema = {
                "request": json_repair.loads(
                    data[i]["message"].split("响应：")[0].split("参数：")[1].strip()
                ),
                "response": json_repair.loads(
                    data[i]["message"].split("响应：")[1].strip()
                ),
                "user_request":json_repair.loads(
                    data[i]["message"].split("响应：")[0].split("参数：")[1].strip()
                )['messages'][-1]['content'].split('客户发来了消息："')[1].split('"，请结合已知的知识简短的回复客户')[0]
            }
            data[i]["message"] = schema
            
            checked_data.append(data[i])
        except Exception as e:
            error_data.append(
                {"index": i, "original_message": data[i]["message"], "error": str(e)}
            )
            print(f"Error at index {i}: {e}")
    print(
        f"split_log_message all_data count :{len(data)}   cheched_data count :{len(checked_data)} error_data count :{len(error_data)}"
    )
    return checked_data, error_data

def split_log_pre_message(data):
    error_data = []
    checked_data = []
    for i in tqdm(range(len(data)), total=len(data), desc="Spliting log message"):
        try:
            data[i]['context'] = json_repair.loads(data[i]["context"])
            schema = {
                "request": json_repair.loads(
                    data[i]["message"].split("响应：")[0].split("参数：")[1].strip()
                ),
                "response": json_repair.loads(
                    data[i]["message"].split("响应：")[1].strip()
                ),
                "user_request":json_repair.loads(
                    data[i]["message"].split("响应：")[0].split("参数：")[1].strip()
                )['messages'][0]['content'].split('【客户】：')[-1].split('</聊天记录>')[0]
            }
            data[i]["message"] = schema
            
            checked_data.append(data[i])
        except Exception as e:
            error_data.append(
                {"index": i, "original_message": data[i]["message"], "error": str(e)}
            )
            print(f"Error at index {i}: {e}")
    print(
        f"split_log_message all_data count :{len(data)}   cheched_data count :{len(checked_data)} error_data count :{len(error_data)}"
    )
    return checked_data, error_data

