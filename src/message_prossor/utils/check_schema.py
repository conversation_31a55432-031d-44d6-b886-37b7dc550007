import fastjsonschema
from tqdm import tqdm


def check_schema(data, json_schema):
    validate_func = fastjsonschema.compile(json_schema)

    error_list = []
    checked_list = []

    for index, item in tqdm(enumerate(data), total=len(data), desc="Checking schema"):
        try:
            validate_func(item["message"]["response"])
            checked_list.append(item)
        except fastjsonschema.JsonSchemaException as e:
            error_list.append({"error_message": str(e), "item": item, "index": index})

    print(
        f"check_schema all_data count: {len(data)} human_data count: {len(checked_list)} error_data count: {len(error_list)}"
    )
    return checked_list, error_list
